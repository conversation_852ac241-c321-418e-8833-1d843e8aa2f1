import { useState, useEffect, useMemo } from "react";
import { useClaims } from "@/hooks/useClaim";
import { useSearchParams } from "next/navigation";

export interface Article {
  cobertura: string;
  articulo: string;
  valor: string;
  documentos?: File[];
}

export interface ClaimFormData {
  // TalkAbout data
  fecha: string;
  hora: string;
  lugar: string;
  descripcion: string;
  articles: Article[];
  
  // BeforeEnd data
  ocupacion: string;
  empresa: string;
  certificadoDigital: string;
  curp: string;
  rfc: string;
  clabe: string;
  // genero: string;
  funcionarioGobierno: string;
  conyuge: string;
  negocioPropio: string;
  actaNombrePropio: boolean;
  
  // Documents
  identificacionOficial: File | null;
  comprobanteDomicilio: File | null;
  constanciaCurp: File | null;
  constanciaSituacionFiscal: File | null;
  caratulaBancaria: File | null;
}

interface UseClaimFormProps {
  onValidateForm?: (section: 'talkAbout' | 'beforeEnd', isValid: boolean) => void;
}

export const useClaimForm = ({ onValidateForm }: UseClaimFormProps = {}) => {
  const { remainingCoverage, fetchRemainingCoverage, claims } = useClaims();
  const searchParams = useSearchParams();
  const claimId = searchParams.get('claim');
  
  const selectedClaim = claimId ? claims.find(claim => claim.id.toString() === claimId) : null;
  const polizaId = selectedClaim?.poliza;

  const [formData, setFormData] = useState<ClaimFormData>({
    // TalkAbout initial values
    fecha: '',
    hora: '',
    lugar: '',
    descripcion: '',
    articles: [],
    
    // BeforeEnd initial values
    ocupacion: '',
    empresa: '',
    certificadoDigital: '',
    curp: '',
    rfc: '',
    clabe: '',
    // genero: '',
    funcionarioGobierno: '',
    conyuge: '',
    negocioPropio: '',
    actaNombrePropio: false,
    
    // Documents initial values
    identificacionOficial: null,
    comprobanteDomicilio: null,
    constanciaCurp: null,
    constanciaSituacionFiscal: null,
    caratulaBancaria: null,
  });

  const [errors, setErrors] = useState<{ [key: string]: boolean }>({});
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const handleInputChange = (field: keyof ClaimFormData, value: string | boolean | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: false }));
    }
  };

  const addArticle = (article: Article) => {
    if (editingIndex !== null) {
      setFormData(prev => ({
        ...prev,
        articles: prev.articles.map((item, index) =>
          index === editingIndex ? article : item
        )
      }));
      setEditingIndex(null);
    } else {
      setFormData(prev => ({
        ...prev,
        articles: [...prev.articles, article]
      }));
    }
  };

  const editArticle = (index: number) => {
    setEditingIndex(index);
    return formData.articles[index];
  };

  const deleteArticle = (index: number) => {
    setFormData(prev => ({
      ...prev,
      articles: prev.articles.filter((_, i) => i !== index)
    }));
  };

  const handleFileUpload = (field: keyof ClaimFormData, file: File | null) => {
    handleInputChange(field, file);
  };

  const removeFile = (field: keyof ClaimFormData) => {
    handleInputChange(field, null);
  };

  const calculateTotal = () => {
    return formData.articles.reduce((sum, article) => sum + parseFloat(article.valor || '0'), 0);
  };

  const calculateRemainingCoverage = () => {
    const totalClaimed = calculateTotal();
    return remainingCoverage - totalClaimed;
  };

  // TalkAbout validation
  const isTalkAboutValid = useMemo(() => {
    return formData.fecha !== '' &&
      formData.hora !== '' &&
      formData.lugar !== '' &&
      formData.descripcion !== '' &&
      formData.articles.length > 0 &&
      calculateRemainingCoverage() >= 0;
  }, [formData.fecha, formData.hora, formData.lugar, formData.descripcion, formData.articles, remainingCoverage]);

  // BeforeEnd validation
  const isBeforeEndValid = useMemo(() => {
    return formData.ocupacion.trim() !== '' &&
      formData.empresa.trim() !== '' &&
      formData.curp.trim() !== '' &&
      formData.rfc.trim() !== '' &&
      formData.clabe.trim() !== '' &&
      // formData.genero !== '' &&
      formData.funcionarioGobierno !== '' &&
      formData.conyuge !== '' &&
      formData.negocioPropio !== '' &&
      formData.identificacionOficial !== null &&
      formData.comprobanteDomicilio !== null &&
      formData.constanciaCurp !== null &&
      formData.caratulaBancaria !== null;
  }, [formData]);

  const validateTalkAbout = () => {
    const newErrors: { [key: string]: boolean } = {};

    if (!formData.fecha) newErrors.fecha = true;
    if (!formData.hora) newErrors.hora = true;
    if (!formData.lugar) newErrors.lugar = true;
    if (!formData.descripcion) newErrors.descripcion = true;
    if (formData.articles.length === 0) newErrors.articles = true;
    if (calculateRemainingCoverage() < 0) newErrors.coverage = true;

    setErrors(prev => ({ ...prev, ...newErrors }));
    return Object.keys(newErrors).length === 0;
  };

  const validateBeforeEnd = () => {
    const newErrors: { [key: string]: boolean } = {};
    
    if (!formData.ocupacion.trim()) newErrors.ocupacion = true;
    if (!formData.empresa.trim()) newErrors.empresa = true;
    if (!formData.curp.trim()) newErrors.curp = true;
    if (!formData.rfc.trim()) newErrors.rfc = true;
    if (!formData.clabe.trim()) newErrors.clabe = true;
    // if (!formData.genero) newErrors.genero = true;
    if (!formData.funcionarioGobierno) newErrors.funcionarioGobierno = true;
    if (!formData.conyuge) newErrors.conyuge = true;
    if (!formData.negocioPropio) newErrors.negocioPropio = true;
    if (!formData.identificacionOficial) newErrors.identificacionOficial = true;
    if (!formData.comprobanteDomicilio) newErrors.comprobanteDomicilio = true;
    if (!formData.constanciaCurp) newErrors.constanciaCurp = true;
    if (!formData.caratulaBancaria) newErrors.caratulaBancaria = true;
    
    setErrors(prev => ({ ...prev, ...newErrors }));
    return Object.keys(newErrors).length === 0;
  };

  const submitForm = async () => {
    const talkAboutValid = validateTalkAbout();
    const beforeEndValid = validateBeforeEnd();

    if (!talkAboutValid || !beforeEndValid) {
      return { success: false, message: 'Por favor completa todos los campos requeridos' };
    }

    try {
      // const formDataToSend = new FormData();

      // // Add basic form data
      // Object.entries(formData).forEach(([key, value]) => {
      //   if (key === 'articles') {
      //     formDataToSend.append(key, JSON.stringify(value));
      //   } else if (value instanceof File) {
      //     formDataToSend.append(key, value);
      //   } else if (typeof value === 'boolean') {
      //     formDataToSend.append(key, value.toString());
      //   } else if (value && typeof value === 'string') {
      //     formDataToSend.append(key, value);
      //   }
      // });

      // // Add claim metadata
      // if (claimId) formDataToSend.append('claimId', claimId);
      // if (polizaId) formDataToSend.append('polizaId', polizaId.toString());

      // // Here you would make the API call to submit the form
      // // const response = await fetch('/api/claims/submit', {
      // //   method: 'POST',
      // //   body: formDataToSend,
      // // });

      // console.log('Form submitted:', Object.fromEntries(formDataToSend));
      return { success: true, message: 'Formulario enviado correctamente' };
    } catch (error) {
      console.error('Error submitting form:', error);
      return { success: false, message: 'Error al enviar el formulario' };
    }
  };

  useEffect(() => {
    if (polizaId) {
      fetchRemainingCoverage(polizaId);
    }
  }, [polizaId, fetchRemainingCoverage]);

  useEffect(() => {
    if (onValidateForm) {
      onValidateForm('talkAbout', isTalkAboutValid);
    }
  }, [isTalkAboutValid, onValidateForm]);

  useEffect(() => {
    if (onValidateForm) {
      onValidateForm('beforeEnd', isBeforeEndValid);
    }
  }, [isBeforeEndValid, onValidateForm]);

  return {
    formData,
    errors,
    editingIndex,
    selectedClaim,
    remainingCoverage,
    handleInputChange,
    addArticle,
    editArticle,
    deleteArticle,
    setEditingIndex,
    handleFileUpload,
    removeFile,
    calculateTotal,
    calculateRemainingCoverage,
    validateTalkAbout,
    validateBeforeEnd,
    submitForm,
    isTalkAboutValid,
    isBeforeEndValid,
  };
};