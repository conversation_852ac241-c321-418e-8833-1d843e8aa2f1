/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from "@/core/axios";
import { Claim } from "@/hooks/useClaim";
import { APIClaimResponse, ClaimQueryParams, ClaimDocumentsResponse } from "@/types/claims";

export class ClaimService {
  private baseUrl = "v1/claims";

  async getDocuments() {
    const res = await axios.get(`${this.baseUrl}/documents/required`);

    return res.data as Claim[];
  }

  async getClaims(params?: ClaimQueryParams): Promise<APIClaimResponse> {
    const res = await axios.get(`${this.baseUrl}/me`, { params });
    return res.data as APIClaimResponse;
  }

  async remainingCoverage(
    id: string
  ): Promise<{ idPoliza: number; montoAsegurado: number }> {
    const res = await axios.post(`${this.baseUrl}/remaining-coverage`, {
      idPoliza: id,
    });

    return res.data;
  }

  /**
   * Obtiene todos los documentos de una reclamación específica
   * @param claimId ID de la reclamación
   * @returns Respuesta completa con documentos de la reclamación
   */
  async getClaimDocuments(claimId: string): Promise<ClaimDocumentsResponse> {
    const res = await axios.get(`${this.baseUrl}/${claimId}/documents`);
    return res.data as ClaimDocumentsResponse;
  }

  /**
   * Actualiza un documento de reclamación existente
   * @param idDocument ID del documento de reclamación a actualizar
   * @param file Archivo a subir (PDF o JPG, máximo 5MB)
   * @returns Respuesta de la actualización del documento
   */
  async updateClaimDocument(idDocument: string, file: File): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const url = `${this.baseUrl}/documents/${idDocument}/document/update`;

    const res = await axios.patch(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return res.data;
  }
}
