import { useState } from "react";
import { Button } from "@mui/material";
import DocumentField from "./DocumentField";

const btnStyles = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: "#10265F",
  borderRadius: "10px",
  height: "40px",
  minHeight: '40px',
  width: "40px",
  textTransform: "none",
  fontSize: "0.9rem",
  fontWeight: 500,
  "&:hover": {
    backgroundColor: "#51519b",
  },
};

interface FormData {
  nombres: string;
  apellidoPaterno: string;
  apellidoMaterno: string;
  genero: string;
  fechaNacimiento: string;
  tipoIdentificacion: string;
}

interface FormErrors {
  nombres?: string;
  apellidoPaterno?: string;
  apellidoMaterno?: string;
  genero?: string;
  fechaNacimiento?: string;
  tipoIdentificacion?: string;
  identificacion?: string;
}

const PersonalForm = () => {
  const [files, setFiles] = useState<Record<string, File | null>>({});
  const [formData, setFormData] = useState<FormData>({
    nombres: '',
    apellidoPaterno: '',
    apellidoMaterno: '',
    genero: '',
    fechaNacimiento: '',
    tipoIdentificacion: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const handleFileUpload = (fieldName: string, uploadedFile: File | null) => {
    setFiles(prev => ({ ...prev, [fieldName]: uploadedFile }));
    if (uploadedFile) {
      setErrors(prev => ({ ...prev, identificacion: undefined }));
    }
  };

  const removeFile = (fieldName: string) => {
    setFiles(prev => ({ ...prev, [fieldName]: null }));
    setErrors(prev => ({ ...prev, identificacion: 'La identificación es requerida' }));
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (value.trim()) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    if (!formData.nombres.trim()) newErrors.nombres = 'Los nombres son requeridos';
    if (!formData.apellidoPaterno.trim()) newErrors.apellidoPaterno = 'El apellido paterno es requerido';
    if (!formData.apellidoMaterno.trim()) newErrors.apellidoMaterno = 'El apellido materno es requerido';
    if (!formData.genero.trim()) newErrors.genero = 'El género es requerido';
    if (!formData.fechaNacimiento.trim()) newErrors.fechaNacimiento = 'La fecha de nacimiento es requerida';
    if (!formData.tipoIdentificacion.trim()) newErrors.tipoIdentificacion = 'El tipo de identificación es requerido';
    if (!files.archivoIdentificacion) newErrors.identificacion = 'La identificación es requerida';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isFormValid = () => {
    return formData.nombres.trim() && 
      formData.apellidoPaterno.trim() && 
      formData.apellidoMaterno.trim() && 
      formData.genero.trim() && 
      formData.fechaNacimiento.trim() && 
      formData.tipoIdentificacion.trim() && 
      files.archivoIdentificacion !== null && 
      files.archivoIdentificacion !== undefined;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      console.log('Form is valid, proceeding...', { formData, files });
    }
  };

  return (
    <form>
      <strong style={{ fontSize: "1.7rem" }}>Cambio de datos personales</strong>
      <p style={{ fontSize: "1.2rem", fontWeight: 500 }}>
        Ingresa tus nuevos datos personales y carga los documentos solicitados.
      </p>
      <div style={{ display: "flex", gap: "1rem", marginTop: "3rem", justifyContent: "space-between", flexDirection: "column" }}>
        <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600 }}>
          <label>Nombres</label>
          <input 
            placeholder="Nombres" 
            type="text" 
            name="nombres" 
            value={formData.nombres}
            onChange={(e) => handleInputChange('nombres', e.target.value)}
            required 
            style={{ 
              border: `3px solid ${errors.nombres ? '#ef4444' : '#51519b'}`, 
              borderRadius: "15px", 
              padding: "8px"
            }}
          />
          {errors.nombres && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.nombres}</span>}
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, }} >
          <label>Apellido Paterno</label>
          <input 
            placeholder="Apellido Paterno" 
            type="text" 
            name="apellidoPaterno" 
            value={formData.apellidoPaterno}
            onChange={(e) => handleInputChange('apellidoPaterno', e.target.value)}
            required 
            style={{ 
              border: `3px solid ${errors.apellidoPaterno ? '#ef4444' : '#51519b'}`, 
              borderRadius: "15px", 
              padding: "8px"
            }} 
          />
          {errors.apellidoPaterno && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.apellidoPaterno}</span>}
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, }} >
          <label>Apellido Materno</label>
          <input 
            placeholder="Apellido Materno" 
            type="text" 
            name="apellidoMaterno" 
            value={formData.apellidoMaterno}
            onChange={(e) => handleInputChange('apellidoMaterno', e.target.value)}
            required 
            style={{ 
              border: `3px solid ${errors.apellidoMaterno ? '#ef4444' : '#51519b'}`, 
              borderRadius: "15px", 
              padding: "8px"
            }} 
          />
          {errors.apellidoMaterno && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.apellidoMaterno}</span>}
        </div>

        {/* Género y Fecha de nacimiento en una fila */}
        <div style={{ display: "flex", gap: "2rem", justifyContent: "space-between", flexDirection: "row", }} >
          {/* Género */}
          <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, flex: 1, }} >
            <label>Género</label>
            <div style={{ display: "flex", gap: "2rem", alignItems: "center" }}>
              <label style={{ display: "flex", alignItems: "center", gap: "8px", fontWeight: 400, cursor: "pointer", }} >
                <input 
                  type="radio" 
                  name="genero" 
                  value="femenino" 
                  checked={formData.genero === 'femenino'}
                  onChange={(e) => handleInputChange('genero', e.target.value)}
                  style={{ margin: 0, transform: "scale(1.2)" }} 
                />
                Femenino
              </label>
              <label style={{ display: "flex", alignItems: "center", gap: "8px", fontWeight: 400, cursor: "pointer", }} >
                <input 
                  type="radio" 
                  name="genero" 
                  value="masculino" 
                  checked={formData.genero === 'masculino'}
                  onChange={(e) => handleInputChange('genero', e.target.value)}
                  style={{ margin: 0, transform: "scale(1.2)" }} 
                />
                Masculino
              </label>
            </div>
            {errors.genero && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.genero}</span>}
          </div>

          {/* Fecha de nacimiento */}
          <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, flex: 1, }} >
            <label>Fecha de nacimiento</label>
            <input 
              type="date" 
              name="fechaNacimiento" 
              value={formData.fechaNacimiento}
              onChange={(e) => handleInputChange('fechaNacimiento', e.target.value)}
              required 
              style={{ 
                border: `3px solid ${errors.fechaNacimiento ? '#ef4444' : '#51519b'}`, 
                borderRadius: "15px", 
                padding: "8px"
              }}
            />
            {errors.fechaNacimiento && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.fechaNacimiento}</span>}
          </div>
        </div>

        {/* Identificación oficial */}
        <div style={{ display: "flex", flexDirection: "column", gap: "1rem", marginTop: "2rem", }} >
          <h3 style={{ fontSize: "1.1rem", fontWeight: 600, marginBottom: "0.5rem", }} >
            Sube una identificación oficial
          </h3>
          <p style={{ fontSize: "1rem", color: "#64748b", margin: 0 }}>
            Selecciona el tipo de identificación que deseas registrar
          </p>

          {/* Tipos de identificación */}
          <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, }} >
            <div style={{ display: "flex", gap: "2rem", alignItems: "center", flexWrap: "wrap", }} >
              <label style={{ display: "flex", alignItems: "center", gap: "8px", fontWeight: 400, cursor: "pointer", }} >
                <input 
                  type="radio" 
                  name="tipoIdentificacion" 
                  value="ine" 
                  checked={formData.tipoIdentificacion === 'ine'}
                  onChange={(e) => handleInputChange('tipoIdentificacion', e.target.value)}
                  style={{ margin: 0, transform: "scale(1.2)" }} 
                />
                INE
              </label>
              <label style={{ display: "flex", alignItems: "center", gap: "8px", fontWeight: 400, cursor: "pointer", }} >
                <input 
                  type="radio" 
                  name="tipoIdentificacion" 
                  value="pasaporte" 
                  checked={formData.tipoIdentificacion === 'pasaporte'}
                  onChange={(e) => handleInputChange('tipoIdentificacion', e.target.value)}
                  style={{ margin: 0, transform: "scale(1.2)" }} 
                />
                Pasaporte
              </label>
              <label style={{ display: "flex", alignItems: "center", gap: "8px", fontWeight: 400, cursor: "pointer", }} >
                <input 
                  type="radio" 
                  name="tipoIdentificacion" 
                  value="curp" 
                  checked={formData.tipoIdentificacion === 'curp'}
                  onChange={(e) => handleInputChange('tipoIdentificacion', e.target.value)}
                  style={{ margin: 0, transform: "scale(1.2)" }} 
                />
                CURP
              </label>
              <label style={{ display: "flex", alignItems: "center", gap: "8px", fontWeight: 400, cursor: "pointer", }} >
                <input 
                  type="radio" 
                  name="tipoIdentificacion" 
                  value="acta" 
                  checked={formData.tipoIdentificacion === 'acta'}
                  onChange={(e) => handleInputChange('tipoIdentificacion', e.target.value)}
                  style={{ margin: 0, transform: "scale(1.2)" }} 
                />
                Acta de nacimiento
              </label>
            </div>
            {errors.tipoIdentificacion && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.tipoIdentificacion}</span>}
          </div>

          {/* Subir archivo */}
          <DocumentField 
            description="Subir PDF o JPG" 
            fieldName="archivoIdentificacion" 
            file={files.archivoIdentificacion || null} 
            onFileUpload={handleFileUpload} 
            onRemoveFile={removeFile} 
            hasError={!!errors.identificacion}  
            errorMessage={errors.identificacion}
            required={true}
          />
        </div>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginTop: '4rem', gap: '1rem' }}>
        <span style={{ color: 'gray', textAlign: 'center' }}>
          {isFormValid() ? 'Todo listo para continuar' : 'Completa toda la información antes de continuar'}
        </span>
        <Button 
          variant="contained" 
          onClick={handleSubmit}
          disabled={!isFormValid()}
          sx={{ 
            ...btnStyles, 
            alignSelf: "center", 
            borderRadius: "20px", 
            height: "60px", 
            minHeight: "60px", 
            width: "200px",
            opacity: isFormValid() ? 1 : 0.5,
            cursor: isFormValid() ? 'pointer' : 'not-allowed'
          }} 
        >
          Continuar
        </Button>
      </div>
    </form>
  );
};

export default PersonalForm;
