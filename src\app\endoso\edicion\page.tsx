'use client';

import { But<PERSON> } from "@mui/material";
import { ArrowLeft } from "lucide-react";
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense } from "react";
import PersonalForm from '../components/PersonalForm';
import DomicilioForm from '../components/DomicilioForm';

const screenOverlayStyle = {
  position: 'fixed' as const,
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 1000,
};

const screenContentStyle = {
  display: 'flex',
  flexDirection: 'column' as const,
  backgroundColor: '#fff',
  padding: '3rem',
  gap: '2rem',
  width: '100vw',
  height: '100vh',
  overflow: 'auto',
  position: 'relative' as const,
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
};

const btnStyles = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: "#10265F",
  borderRadius: "10px",
  height: "40px",
  minHeight: '40px',
  width: "40px",
  textTransform: "none",
  fontSize: "0.9rem",
  fontWeight: 500,
  "&:hover": {
    backgroundColor: "#51519b",
  },
};

const EdicionContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const type = searchParams.get('type');

  return (
    <div style={screenOverlayStyle}>
      <div style={screenContentStyle}>
        <Button variant="contained" onClick={() => router.push('/endoso/select')} sx={btnStyles}>
          <ArrowLeft />
        </Button>

        {type === 'personal' && <PersonalForm/>}
        {type === 'domicilio' && <DomicilioForm />}
        
        {!type && (
          <div style={{ textAlign: "center", padding: "40px", backgroundColor: "#f8fafc", borderRadius: "16px",
              border: "2px solid #e2e8f0",
            }}
          >
            <p style={{ fontSize: "18px", color: "#64748b", margin: "0" }}>
              Por favor, especifica un tipo de formulario válido (personal o domicilio) en los parámetros de la URL.
            </p>
          </div>
        )}

        {type && type !== 'personal' && type !== 'domicilio' && (
          <div style={{ textAlign: "center", padding: "40px", backgroundColor: "#f8fafc", borderRadius: "16px", border: "2px solid #e2e8f0" }} >
            <p style={{ fontSize: "18px", color: "#64748b", margin: "0" }} >
              Tipo de formulario no válido. Solo se aceptan los tipos personal o domicilio.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default function EdicionPage() {
  return (
    <Suspense fallback={<div>Cargando...</div>}>
      <EdicionContent />
    </Suspense>
  );
}
