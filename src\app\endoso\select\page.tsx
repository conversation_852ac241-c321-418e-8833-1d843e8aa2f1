"use client";
import { Button, useMediaQuery } from "@mui/material";
import { ArrowLeft, User, Home, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import EndorsementCard from "../components/EndorsementCard";

export default function EndorsementProcess() {
  const router = useRouter();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const isTablet = useMediaQuery("(max-width: 1024px)");

  const handlePolicyCancellation = () => {
    console.log("Cancelación de póliza");
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        backgroundColor: "#f8f9fa",
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      }}
    >
      {/* Header with back button */}
      <div
        style={{
          padding: "20px",
          paddingBottom: "40px",
        }}
      >
        <Button
          variant="contained"
          onClick={() => router.push("/insurance-policies")}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "#1e3a8a",
            borderRadius: "12px",
            height: "48px",
            minHeight: "48px",
            width: "48px",
            textTransform: "none",
            fontSize: "0.9rem",
            fontWeight: 500,
            "&:hover": {
              backgroundColor: "#1e40af",
            },
          }}
        >
          <ArrowLeft size={20} />
        </Button>
      </div>

      {/* Main content */}
      <div
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          padding: "0 20px",
          textAlign: "center",
        }}
      >
        {/* Title */}
        <h1
          style={{
            fontSize: isMobile ? "28px" : "36px",
            fontWeight: "700",
            color: "#1e3a8a",
            marginBottom: "20px",
            lineHeight: "1.2",
          }}
        >
          Inicia tu proceso de endoso
        </h1>

        {/* Subtitle */}
        <p
          style={{
            fontSize: isMobile ? "16px" : "18px",
            color: "#64748b",
            marginBottom: "60px",
            maxWidth: "600px",
            margin: "0 auto 60px auto",
            lineHeight: "1.5",
          }}
        >
          Selecciona el tipo de cambio que deseas realizar en tu póliza
        </p>

        {/* Cards container */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: isMobile
              ? "1fr"
              : isTablet
                ? "repeat(2, 1fr)"
                : "repeat(3, 1fr)",
            gap: "32px",
            maxWidth: "1000px",
            margin: "0 auto",
            padding: "0 20px",
          }}
        >
          <EndorsementCard
            title="Cambio de datos personales"
            description="Modifica el nombre del asegurado en tu póliza"
            icon={<User size={28} color="white" strokeWidth={1.5} />}
            onClick={() => router.push("/endoso/edicion?type=personal")}
          />

          <EndorsementCard
            title="Cambio de domicilio"
            description="Actualiza la dirección registrada en tu póliza"
            icon={<Home size={28} color="white" />}
            onClick={() => router.push("/endoso/edicion?type=domicilio")}
          />

          <EndorsementCard
            title="Cancelación de póliza"
            description="Solicita la baja anticipada de esta póliza"
            icon={<XCircle size={28} color="white" />}
            onClick={handlePolicyCancellation}
          />
        </div>
      </div>
    </div>
  );
}
