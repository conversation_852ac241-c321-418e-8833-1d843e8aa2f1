import { Button } from "@mui/material";
import DocumentField from "./DocumentField";
import { useState } from "react";

const btnStyles = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: "#10265F",
  borderRadius: "10px",
  height: "40px",
  minHeight: '40px',
  width: "40px",
  textTransform: "none",
  fontSize: "0.9rem",
  fontWeight: 500,
  "&:hover": {
    backgroundColor: "#51519b",
  },
};

interface FormData {
  calle: string;
  colonia: string;
  municipio: string;
  estado: string;
  numeroExterior: string;
  numeroInterior: string;
  codigoPostal: string;
}

interface FormErrors {
  calle?: string;
  colonia?: string;
  municipio?: string;
  estado?: string;
  numeroExterior?: string;
  numeroInterior?: string;
  codigoPostal?: string;
  comprobante?: string;
}

const DomicilioForm = () => {
  const [files, setFiles] = useState<Record<string, File | null>>({});
  const [formData, setFormData] = useState<FormData>({
    calle: '',
    colonia: '',
    municipio: '',
    estado: '',
    numeroExterior: '',
    numeroInterior: '',
    codigoPostal: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  
    const handleFileUpload = (fieldName: string, uploadedFile: File | null) => {
      setFiles(prev => ({ ...prev, [fieldName]: uploadedFile }));
      if (uploadedFile) {
        setErrors(prev => ({ ...prev, comprobante: undefined }));
      }
    };
  
    const removeFile = (fieldName: string) => {
      setFiles(prev => ({ ...prev, [fieldName]: null }));
      setErrors(prev => ({ ...prev, comprobante: 'El comprobante de domicilio es requerido' }));
    };

    const handleInputChange = (field: keyof FormData, value: string) => {
      setFormData(prev => ({ ...prev, [field]: value }));
      if (value.trim()) {
        setErrors(prev => ({ ...prev, [field]: undefined }));
      }
    };

    const validateForm = (): boolean => {
      const newErrors: FormErrors = {};
      
      if (!formData.calle.trim()) newErrors.calle = 'La calle es requerida';
      if (!formData.colonia.trim()) newErrors.colonia = 'La colonia es requerida';
      if (!formData.municipio.trim()) newErrors.municipio = 'El municipio es requerido';
      if (!formData.estado.trim()) newErrors.estado = 'El estado es requerido';
      if (!formData.numeroExterior.trim()) newErrors.numeroExterior = 'El número exterior es requerido';
      if (!formData.numeroInterior.trim()) newErrors.numeroInterior = 'El número interior es requerido';
      if (!formData.codigoPostal.trim()) newErrors.codigoPostal = 'El código postal es requerido';
      if (!files.archivoIdentificacion) newErrors.comprobante = 'El comprobante de domicilio es requerido';
      
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    const isFormValid = () => {
      return formData.calle.trim() && 
        formData.colonia.trim() && 
        formData.municipio.trim() && 
        formData.estado.trim() && 
        formData.numeroExterior.trim() && 
        formData.numeroInterior.trim() && 
        formData.codigoPostal.trim() && 
        files.archivoIdentificacion !== null && 
        files.archivoIdentificacion !== undefined;
    };

    const handleSubmit = () => {
      if (validateForm()) {
        console.log('Form is valid, proceeding...', { formData, files });
      }
    };
  return (
    <form>
       <strong style={{ fontSize: "1.7rem" }}>Cambio de domicilio</strong>
      <p style={{ fontSize: "1.2rem", fontWeight: 500 }}>
        Completa la información de tu nuevo domicilio y adjunta tu comprobante
      </p>
      <div style={{ display: "flex", gap: "1rem", marginTop: "3rem", justifyContent: "space-between", flexDirection: "column" }}>
        <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600 }}>
          <label>Calle</label>
          <input 
            placeholder="Calle" 
            type="text" 
            name="calle" 
            value={formData.calle}
            onChange={(e) => handleInputChange('calle', e.target.value)}
            required 
            style={{ 
              border: `3px solid ${errors.calle ? '#ef4444' : '#51519b'}`, 
              borderRadius: "15px", 
              padding: "8px"
            }}
          />
          {errors.calle && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.calle}</span>}
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, }} >
          <label>Colonia</label>
          <input 
            placeholder="Colonia" 
            type="text" 
            name="colonia" 
            value={formData.colonia}
            onChange={(e) => handleInputChange('colonia', e.target.value)}
            required 
            style={{ 
              border: `3px solid ${errors.colonia ? '#ef4444' : '#51519b'}`, 
              borderRadius: "15px", 
              padding: "8px"
            }} 
          />
          {errors.colonia && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.colonia}</span>}
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, }} >
          <label>Municipio</label>
          <input 
            placeholder="Municipio" 
            type="text" 
            name="municipio" 
            value={formData.municipio}
            onChange={(e) => handleInputChange('municipio', e.target.value)}
            required 
            style={{ 
              border: `3px solid ${errors.municipio ? '#ef4444' : '#51519b'}`, 
              borderRadius: "15px", 
              padding: "8px"
            }} 
          />
          {errors.municipio && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.municipio}</span>}
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, }} >
          <label>Estado</label>
          <input 
            placeholder="Estado" 
            type="text" 
            name="estado" 
            value={formData.estado}
            onChange={(e) => handleInputChange('estado', e.target.value)}
            required 
            style={{ 
              border: `3px solid ${errors.estado ? '#ef4444' : '#51519b'}`, 
              borderRadius: "15px", 
              padding: "8px"
            }} 
          />
          {errors.estado && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.estado}</span>}
        </div>

        <div style={{ display: "flex", gap: "2rem", justifyContent: "space-between", flexDirection: "row", }} >
          <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600 }}>
            <label>Número exterior</label>
            <input 
              placeholder="Numero exterior" 
              type="text" 
              name="numeroExterior" 
              value={formData.numeroExterior}
              onChange={(e) => handleInputChange('numeroExterior', e.target.value)}
              required 
              style={{ 
                border: `3px solid ${errors.numeroExterior ? '#ef4444' : '#51519b'}`, 
                borderRadius: "15px", 
                padding: "8px", 
                width: "100%" 
              }}
            />
            {errors.numeroExterior && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.numeroExterior}</span>}
          </div>

          <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, }} >
            <label>Número interior</label>
            <input 
              placeholder="Número interior" 
              type="text" 
              name="numeroInterior" 
              value={formData.numeroInterior}
              onChange={(e) => handleInputChange('numeroInterior', e.target.value)}
              required 
              style={{ 
                border: `3px solid ${errors.numeroInterior ? '#ef4444' : '#51519b'}`, 
                borderRadius: "15px", 
                padding: "8px", 
                width: "100%" 
              }} 
            />
            {errors.numeroInterior && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.numeroInterior}</span>}
          </div>

          <div style={{ display: "flex", flexDirection: "column", gap: "10px", fontWeight: 600, }} >
            <label>Código postal</label>
            <input 
              placeholder="Código postal" 
              type="text" 
              name="codigoPostal" 
              value={formData.codigoPostal}
              onChange={(e) => handleInputChange('codigoPostal', e.target.value)}
              required 
              style={{ 
                border: `3px solid ${errors.codigoPostal ? '#ef4444' : '#51519b'}`, 
                borderRadius: "15px", 
                padding: "8px", 
                width: "100%" 
              }} 
            />
            {errors.codigoPostal && <span style={{ color: '#ef4444', fontSize: '0.875rem' }}>{errors.codigoPostal}</span>}
          </div>
        </div>

        {/* Identificación oficial */}
        <div style={{ display: "flex", flexDirection: "column", gap: "1rem", marginTop: "2rem", }} >
          <div>
            <h3 style={{ fontSize: "1.1rem", fontWeight: 600, margin: 0 }} >
              Sube un comprobante de domicilio
            </h3>
            <p style={{ fontSize: "1rem", color: "#64748b", margin: 0 }}>
              Adjunta un comprobante con no más de 3 meses de antigüedad
            </p>
          </div>

          {/* Subir archivo */}
          <DocumentField 
            description="Subir PDF o JPG" 
            fieldName="archivoIdentificacion" 
            file={files.archivoIdentificacion || null} 
            onFileUpload={handleFileUpload} 
            onRemoveFile={removeFile} 
            hasError={!!errors.comprobante}  
            errorMessage={errors.comprobante}
            required={true}
          />
        </div>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginTop: '4rem', gap: '1rem' }}>
        <span style={{ color: 'gray', textAlign: 'center' }}>
          {isFormValid() ? 'Todo listo para continuar' : 'Completa toda la información antes de continuar'}
        </span>
        <Button 
          variant="contained" 
          onClick={handleSubmit}
          disabled={!isFormValid()}
          sx={{ 
            ...btnStyles, 
            alignSelf: "center", 
            borderRadius: "20px", 
            height: "60px", 
            minHeight: "60px", 
            width: "200px",
            opacity: isFormValid() ? 1 : 0.5,
            cursor: isFormValid() ? 'pointer' : 'not-allowed'
          }} 
        >
          Continuar
        </Button>
      </div>
    </form>
  );
};

export default DomicilioForm;
